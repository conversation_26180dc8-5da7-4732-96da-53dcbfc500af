{"rustc": 2020545558504112252, "features": "[]", "declared_features": "[\"nightly_derive\"]", "target": 16548621151911234621, "profile": 9487811443914490533, "path": 1193752767089888681, "deps": [[2828590642173593838, "cfg_if", false, 7925922314324018119], [3060637413840920116, "proc_macro2", false, 6169926713507828213], [4974441333307933176, "syn", false, 14127579892300212409], [17990358020177143287, "quote", false, 11645517506645527867]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall_derive-ae3821f5b9f7c0bb/dep-lib-mockall_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}