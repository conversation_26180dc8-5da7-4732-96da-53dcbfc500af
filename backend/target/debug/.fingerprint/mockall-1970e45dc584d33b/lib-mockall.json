{"rustc": 2020545558504112252, "features": "[]", "declared_features": "[\"nightly\"]", "target": 15010007883002352389, "profile": 4391159073538686960, "path": 5388227551669938837, "deps": [[2828590642173593838, "cfg_if", false, 12537976710778852100], [3016941897346161952, "downcast", false, 10709068082501430562], [7886665781035375288, "fragile", false, 10382944561244019127], [9056619860232277314, "mockall_derive", false, 16263099826512915416], [12516616738327129663, "predicates_tree", false, 587164651858052777], [15863765456528386755, "predicates", false, 7727335117875103174], [17917672826516349275, "lazy_static", false, 11606207557014253330]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall-1970e45dc584d33b/dep-lib-mockall", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}