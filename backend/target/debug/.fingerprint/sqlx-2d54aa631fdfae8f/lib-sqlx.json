{"rustc": 2020545558504112252, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlx-macros\", \"sqlx-postgres\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 4391159073538686960, "path": 11914567463367451257, "deps": [[228475551920078470, "sqlx_macros", false, 863558239875406203], [996810380461694889, "sqlx_core", false, 4904952686606878591], [15634168271133386882, "sqlx_postgres", false, 7292806654021689657]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-2d54aa631fdfae8f/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}