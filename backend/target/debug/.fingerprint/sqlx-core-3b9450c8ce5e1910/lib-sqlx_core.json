{"rustc": 2020545558504112252, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 4391159073538686960, "path": 227218125131455142, "deps": [[5103565458935487, "futures_io", false, 11576398150370043243], [40386456601120721, "percent_encoding", false, 13587369804750508914], [530211389790465181, "hex", false, 5596943709979199784], [788558663644978524, "crossbeam_queue", false, 11644704279957669240], [966925859616469517, "ahash", false, 4095334547831719225], [1162433738665300155, "crc", false, 10281516606590098165], [1464803193346256239, "event_listener", false, 8800864960129761924], [1811549171721445101, "futures_channel", false, 11767383075905936022], [3150220818285335163, "url", false, 13915544038860975195], [3405817021026194662, "hashlink", false, 2366893365511673532], [3646857438214563691, "futures_intrusive", false, 7358122239180221397], [3666196340704888985, "smallvec", false, 9088276582689783544], [3712811570531045576, "byteorder", false, 13678195627100064031], [3722963349756955755, "once_cell", false, 16286977724035364933], [5986029879202738730, "log", false, 4499872194606963670], [6493259146304816786, "indexmap", false, 13236570327345544922], [7620660491849607393, "futures_core", false, 6664168418477290047], [8008191657135824715, "thiserror", false, 16170859237487817292], [8319709847752024821, "uuid", false, 13022812468662122894], [8606274917505247608, "tracing", false, 4349454160504880913], [9689903380558560274, "serde", false, 742021653844835408], [9857275760291862238, "sha2", false, 13718717362844778093], [9897246384292347999, "chrono", false, 6691471730903828656], [10629569228670356391, "futures_util", false, 11998840804742346757], [10862088793507253106, "sqlformat", false, 15376506595298215548], [11295624341523567602, "rustls", false, 3184330121182435131], [12170264697963848012, "either", false, 7930717396100765249], [15932120279885307830, "memchr", false, 15088430398005551360], [16066129441945555748, "bytes", false, 15281802003918656464], [16311359161338405624, "rustls_pemfile", false, 3174824160631392467], [16362055519698394275, "serde_json", false, 8864465478426262744], [16973251432615581304, "tokio_stream", false, 9264012457326567547], [17106256174509013259, "atoi", false, 15222053041109763167], [17531218394775549125, "tokio", false, 2736135873494459207], [17605717126308396068, "paste", false, 15424669385067981225], [17652733826348741533, "webpki_roots", false, 15891062947403868408]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-3b9450c8ce5e1910/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}