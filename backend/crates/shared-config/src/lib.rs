//! 配置管理
//!
//! 这个 crate 负责应用程序的配置管理，包括：
//! - 从环境变量加载配置
//! - 配置验证
//! - 默认值设置

use serde::{Deserialize, Serialize};
use shared_errors::{AppError, Result};
use std::env;

/// 应用程序配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 数据库连接 URL
    pub database_url: String,

    /// JWT 密钥
    pub jwt_secret: String,

    /// WhatsApp 容器端口范围
    pub port_range: PortRange,

    /// WhatsApp 容器镜像
    pub whatsapp_container_image: String,

    /// CORS 允许的源
    pub cors_origins: Vec<String>,

    /// 服务器配置
    pub server: ServerConfig,

    /// 日志配置
    pub log: LogConfig,

    /// 容器配置
    pub container: ContainerConfig,
}

/// 端口范围配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PortRange {
    pub start: u16,
    pub end: u16,
}

impl PortRange {
    /// 检查端口是否在范围内
    pub fn contains(&self, port: u16) -> bool {
        port >= self.start && port <= self.end
    }

    /// 获取范围内的端口数量
    pub fn count(&self) -> u16 {
        self.end - self.start + 1
    }

    /// 验证端口范围
    pub fn validate(&self) -> Result<()> {
        if self.start > self.end {
            return Err(AppError::validation(
                "Port range start must be less than or equal to end",
            ));
        }
        if self.start < 1024 {
            return Err(AppError::validation("Port range start must be >= 1024"));
        }
        // u16 的最大值就是 65535，所以这个检查是多余的
        // if self.end > 65535 {
        //     return Err(AppError::validation("Port range end must be <= 65535"));
        // }
        Ok(())
    }
}

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// 服务器监听地址
    pub host: String,

    /// 服务器监听端口
    pub port: u16,

    /// 请求超时时间（秒）
    pub request_timeout: u64,

    /// 最大请求体大小（字节）
    pub max_request_size: usize,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    /// 日志级别
    pub level: String,

    /// 是否输出到控制台
    pub console: bool,

    /// 日志文件路径（可选）
    pub file: Option<String>,

    /// 是否使用 JSON 格式
    pub json: bool,
}

/// 容器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContainerConfig {
    /// Docker 守护进程 URL
    pub docker_url: String,

    /// 容器网络名称
    pub network: String,

    /// 容器重启策略
    pub restart_policy: String,

    /// 容器内存限制（MB）
    pub memory_limit: u64,

    /// 容器 CPU 限制（核心数）
    pub cpu_limit: f64,

    /// 健康检查间隔（秒）
    pub health_check_interval: u64,
}

impl AppConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self> {
        // 加载 .env 文件（如果存在）
        dotenvy::dotenv().ok();

        let config = Self {
            database_url: get_env_var("DATABASE_URL")?,
            jwt_secret: get_env_var("JWT_SECRET")?,
            port_range: PortRange {
                start: get_env_var_or_default("PORT_RANGE_START", "8000")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("PORT_RANGE_START", "Invalid port number")
                    })?,
                end: get_env_var_or_default("PORT_RANGE_END", "8999")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("PORT_RANGE_END", "Invalid port number")
                    })?,
            },
            whatsapp_container_image: get_env_var_or_default(
                "WHATSAPP_CONTAINER_IMAGE",
                "whatsapp-web-multidevice:latest",
            )?,
            cors_origins: get_env_var_or_default("CORS_ORIGINS", "*")?
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
            server: ServerConfig {
                host: get_env_var_or_default("SERVER_HOST", "0.0.0.0")?,
                port: get_env_var_or_default("SERVER_PORT", "8080")?
                    .parse()
                    .map_err(|_| AppError::invalid_input("SERVER_PORT", "Invalid port number"))?,
                request_timeout: get_env_var_or_default("REQUEST_TIMEOUT", "30")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("REQUEST_TIMEOUT", "Invalid timeout value")
                    })?,
                max_request_size: get_env_var_or_default("MAX_REQUEST_SIZE", "16777216")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("MAX_REQUEST_SIZE", "Invalid size value")
                    })?,
            },
            log: LogConfig {
                level: get_env_var_or_default("LOG_LEVEL", "info")?,
                console: get_env_var_or_default("LOG_CONSOLE", "true")?
                    .parse()
                    .map_err(|_| AppError::invalid_input("LOG_CONSOLE", "Invalid boolean value"))?,
                file: get_env_var_optional("LOG_FILE"),
                json: get_env_var_or_default("LOG_JSON", "false")?
                    .parse()
                    .map_err(|_| AppError::invalid_input("LOG_JSON", "Invalid boolean value"))?,
            },
            container: ContainerConfig {
                docker_url: get_env_var_or_default("DOCKER_URL", "unix:///var/run/docker.sock")?,
                network: get_env_var_or_default("CONTAINER_NETWORK", "whatsapp-saas")?,
                restart_policy: get_env_var_or_default(
                    "CONTAINER_RESTART_POLICY",
                    "unless-stopped",
                )?,
                memory_limit: get_env_var_or_default("CONTAINER_MEMORY_LIMIT", "512")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("CONTAINER_MEMORY_LIMIT", "Invalid memory limit")
                    })?,
                cpu_limit: get_env_var_or_default("CONTAINER_CPU_LIMIT", "1.0")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("CONTAINER_CPU_LIMIT", "Invalid CPU limit")
                    })?,
                health_check_interval: get_env_var_or_default("HEALTH_CHECK_INTERVAL", "30")?
                    .parse()
                    .map_err(|_| {
                        AppError::invalid_input("HEALTH_CHECK_INTERVAL", "Invalid interval")
                    })?,
            },
        };

        // 验证配置
        config.validate()?;

        Ok(config)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证端口范围
        self.port_range.validate()?;

        // 验证 JWT 密钥长度
        if self.jwt_secret.len() < 32 {
            return Err(AppError::validation(
                "JWT secret must be at least 32 characters long",
            ));
        }

        // 验证数据库 URL
        if !self.database_url.starts_with("postgres://")
            && !self.database_url.starts_with("postgresql://")
        {
            return Err(AppError::validation(
                "Database URL must be a PostgreSQL connection string",
            ));
        }

        // 验证服务器端口
        if self.server.port < 1024 {
            return Err(AppError::validation("Server port must be >= 1024"));
        }

        // 验证日志级别
        let valid_levels = ["trace", "debug", "info", "warn", "error"];
        if !valid_levels.contains(&self.log.level.as_str()) {
            return Err(AppError::validation("Invalid log level"));
        }

        // 验证容器配置
        if self.container.memory_limit < 128 {
            return Err(AppError::validation(
                "Container memory limit must be at least 128MB",
            ));
        }

        if self.container.cpu_limit <= 0.0 || self.container.cpu_limit > 16.0 {
            return Err(AppError::validation(
                "Container CPU limit must be between 0.1 and 16.0",
            ));
        }

        Ok(())
    }

    /// 获取服务器绑定地址
    pub fn bind_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }

    /// 检查是否为开发环境
    pub fn is_development(&self) -> bool {
        self.log.level == "debug" || self.log.level == "trace"
    }

    /// 检查是否为生产环境
    pub fn is_production(&self) -> bool {
        !self.is_development()
    }
}

/// 获取必需的环境变量
fn get_env_var(key: &str) -> Result<String> {
    env::var(key).map_err(|_| AppError::EnvironmentVariableNotFound {
        var_name: key.to_string(),
    })
}

/// 获取可选的环境变量
fn get_env_var_optional(key: &str) -> Option<String> {
    env::var(key).ok()
}

/// 获取环境变量或使用默认值
fn get_env_var_or_default(key: &str, default: &str) -> Result<String> {
    Ok(env::var(key).unwrap_or_else(|_| default.to_string()))
}

// ================================
// 测试模块
// ================================

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_port_range() {
        let range = PortRange {
            start: 8000,
            end: 8999,
        };

        assert!(range.contains(8000));
        assert!(range.contains(8500));
        assert!(range.contains(8999));
        assert!(!range.contains(7999));
        assert!(!range.contains(9000));

        assert_eq!(range.count(), 1000);
        assert!(range.validate().is_ok());
    }

    #[test]
    fn test_port_range_validation() {
        // 无效范围：start > end
        let invalid_range = PortRange {
            start: 9000,
            end: 8000,
        };
        assert!(invalid_range.validate().is_err());

        // 无效范围：start < 1024
        let invalid_range = PortRange {
            start: 80,
            end: 8000,
        };
        assert!(invalid_range.validate().is_err());

        // 有效范围：end = 65535
        let valid_range = PortRange {
            start: 8000,
            end: 65535,
        };
        assert!(valid_range.validate().is_ok());
    }

    #[test]
    fn test_config_validation() {
        // 设置测试环境变量
        env::set_var("DATABASE_URL", "postgresql://user:pass@localhost/db");
        env::set_var(
            "JWT_SECRET",
            "this_is_a_very_long_jwt_secret_key_for_testing_purposes",
        );

        let config = AppConfig::from_env().unwrap();
        assert!(config.validate().is_ok());

        // 清理环境变量
        env::remove_var("DATABASE_URL");
        env::remove_var("JWT_SECRET");
    }

    #[test]
    fn test_bind_address() {
        let config = AppConfig {
            database_url: "postgresql://test".to_string(),
            jwt_secret: "test_secret_key_that_is_long_enough".to_string(),
            port_range: PortRange {
                start: 8000,
                end: 8999,
            },
            whatsapp_container_image: "test:latest".to_string(),
            cors_origins: vec!["*".to_string()],
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 3000,
                request_timeout: 30,
                max_request_size: 16777216,
            },
            log: LogConfig {
                level: "info".to_string(),
                console: true,
                file: None,
                json: false,
            },
            container: ContainerConfig {
                docker_url: "unix:///var/run/docker.sock".to_string(),
                network: "test".to_string(),
                restart_policy: "unless-stopped".to_string(),
                memory_limit: 512,
                cpu_limit: 1.0,
                health_check_interval: 30,
            },
        };

        assert_eq!(config.bind_address(), "127.0.0.1:3000");
        assert!(!config.is_development());
        assert!(config.is_production());
    }
}
