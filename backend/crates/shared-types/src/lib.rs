//! 共享类型定义
//! 
//! 这个 crate 包含了整个系统中使用的通用类型定义，包括：
//! - 各种 ID 类型
//! - 枚举类型
//! - 常量定义
//! 
//! 这是依赖关系最底层的 crate，不依赖任何其他业务 crate。

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// ================================
// ID 类型定义
// ================================

/// 租户 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TenantId(pub Uuid);

impl TenantId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for TenantId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for TenantId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// 用户 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct UserId(pub Uuid);

impl UserId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for UserId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for UserId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// WhatsApp 账号 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct WhatsAppAccountId(pub Uuid);

impl WhatsAppAccountId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for WhatsAppAccountId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for WhatsAppAccountId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// 聊天会话 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ChatSessionId(pub Uuid);

impl ChatSessionId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for ChatSessionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for ChatSessionId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// 消息 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct MessageId(pub Uuid);

impl MessageId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for MessageId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for MessageId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// 群发任务 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct BroadcastTaskId(pub Uuid);

impl BroadcastTaskId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for BroadcastTaskId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for BroadcastTaskId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

/// 代理 ID
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ProxyId(pub Uuid);

impl ProxyId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl std::fmt::Display for ProxyId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for ProxyId {
    type Err = uuid::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(Self(Uuid::parse_str(s)?))
    }
}

// ================================
// 枚举类型定义
// ================================

/// 用户角色
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum UserRole {
    /// 系统管理员 - 可以管理所有租户
    SystemAdmin,
    /// 租户管理员 - 可以管理自己租户内的所有资源
    TenantAdmin,
    /// 客服人员 - 只能处理分配给自己的聊天
    CustomerService,
}

impl UserRole {
    /// 检查是否可以管理指定租户
    pub fn can_manage_tenant(&self, user_tenant_id: Option<TenantId>, target_tenant_id: TenantId) -> bool {
        match self {
            UserRole::SystemAdmin => true,
            UserRole::TenantAdmin => user_tenant_id == Some(target_tenant_id),
            UserRole::CustomerService => false,
        }
    }
    
    /// 检查是否可以冒充其他用户
    pub fn can_impersonate(&self) -> bool {
        matches!(self, UserRole::SystemAdmin)
    }
}

/// 订阅计划
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SubscriptionPlan {
    /// 基础版
    Basic { max_ports: u16 },
    /// 专业版
    Pro { max_ports: u16 },
    /// 企业版
    Enterprise { max_ports: u16 },
}

impl SubscriptionPlan {
    /// 获取最大端口数
    pub fn max_ports(&self) -> u16 {
        match self {
            SubscriptionPlan::Basic { max_ports } => *max_ports,
            SubscriptionPlan::Pro { max_ports } => *max_ports,
            SubscriptionPlan::Enterprise { max_ports } => *max_ports,
        }
    }
    
    /// 获取计划名称
    pub fn name(&self) -> &'static str {
        match self {
            SubscriptionPlan::Basic { .. } => "Basic",
            SubscriptionPlan::Pro { .. } => "Pro",
            SubscriptionPlan::Enterprise { .. } => "Enterprise",
        }
    }
}

// ================================
// 常量定义
// ================================

/// 默认的订阅计划配置
pub mod subscription_plans {
    use super::SubscriptionPlan;
    
    pub const BASIC: SubscriptionPlan = SubscriptionPlan::Basic { max_ports: 5 };
    pub const PRO: SubscriptionPlan = SubscriptionPlan::Pro { max_ports: 20 };
    pub const ENTERPRISE: SubscriptionPlan = SubscriptionPlan::Enterprise { max_ports: 100 };
}

/// 系统限制常量
pub mod limits {
    /// 租户名称最大长度
    pub const MAX_TENANT_NAME_LENGTH: usize = 100;
    /// 用户名最大长度
    pub const MAX_USERNAME_LENGTH: usize = 50;
    /// 邮箱最大长度
    pub const MAX_EMAIL_LENGTH: usize = 255;
    /// WhatsApp 账号名称最大长度
    pub const MAX_WHATSAPP_ACCOUNT_NAME_LENGTH: usize = 100;
    /// 消息内容最大长度
    pub const MAX_MESSAGE_CONTENT_LENGTH: usize = 4096;
    /// 群发任务名称最大长度
    pub const MAX_BROADCAST_TASK_NAME_LENGTH: usize = 200;
}

// ================================
// 测试模块
// ================================

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_id_creation_and_conversion() {
        let tenant_id = TenantId::new();
        let uuid = tenant_id.as_uuid();
        let tenant_id2 = TenantId::from_uuid(uuid);
        assert_eq!(tenant_id, tenant_id2);
    }
    
    #[test]
    fn test_id_string_conversion() {
        let user_id = UserId::new();
        let id_str = user_id.to_string();
        let parsed_id: UserId = id_str.parse().unwrap();
        assert_eq!(user_id, parsed_id);
    }
    
    #[test]
    fn test_user_role_permissions() {
        let tenant_id = TenantId::new();
        let other_tenant_id = TenantId::new();
        
        // 系统管理员可以管理任何租户
        assert!(UserRole::SystemAdmin.can_manage_tenant(None, tenant_id));
        assert!(UserRole::SystemAdmin.can_manage_tenant(Some(other_tenant_id), tenant_id));
        
        // 租户管理员只能管理自己的租户
        assert!(UserRole::TenantAdmin.can_manage_tenant(Some(tenant_id), tenant_id));
        assert!(!UserRole::TenantAdmin.can_manage_tenant(Some(other_tenant_id), tenant_id));
        
        // 客服人员不能管理任何租户
        assert!(!UserRole::CustomerService.can_manage_tenant(Some(tenant_id), tenant_id));
    }
    
    #[test]
    fn test_subscription_plan() {
        let basic = subscription_plans::BASIC;
        assert_eq!(basic.max_ports(), 5);
        assert_eq!(basic.name(), "Basic");
        
        let pro = subscription_plans::PRO;
        assert_eq!(pro.max_ports(), 20);
        assert_eq!(pro.name(), "Pro");
    }
}
