//! 统一错误处理
//!
//! 这个 crate 定义了整个系统中使用的错误类型和错误处理机制。
//! 所有其他 crate 都应该使用这里定义的错误类型。

use serde::{Deserialize, Serialize};
use shared_types::*;
use thiserror::Error;

/// 应用程序的主要错误类型
#[derive(Debug, Error, Serialize, Deserialize)]
pub enum AppError {
    // ================================
    // 数据库相关错误
    // ================================
    #[error("Database error: {message}")]
    Database { message: String },

    #[error("Database connection failed: {message}")]
    DatabaseConnection { message: String },

    #[error("Database migration failed: {message}")]
    DatabaseMigration { message: String },

    // ================================
    // 验证相关错误
    // ================================
    #[error("Validation error: {message}")]
    Validation { message: String },

    #[error("Invalid input: {field} - {message}")]
    InvalidInput { field: String, message: String },

    #[error("Required field missing: {field}")]
    RequiredFieldMissing { field: String },

    // ================================
    // 认证和授权错误
    // ================================
    #[error("Authentication failed")]
    AuthenticationFailed,

    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("Token expired")]
    TokenExpired,

    #[error("Token invalid")]
    TokenInvalid,

    #[error("Token revoked")]
    TokenRevoked,

    #[error("Permission denied")]
    PermissionDenied,

    #[error("Insufficient privileges")]
    InsufficientPrivileges,

    // ================================
    // 资源相关错误
    // ================================
    #[error("Tenant not found: {id}")]
    TenantNotFound { id: String },

    #[error("User not found: {id}")]
    UserNotFound { id: String },

    #[error("WhatsApp account not found: {id}")]
    WhatsAppAccountNotFound { id: String },

    #[error("Chat session not found: {id}")]
    ChatSessionNotFound { id: String },

    #[error("Message not found: {id}")]
    MessageNotFound { id: String },

    #[error("Broadcast task not found: {id}")]
    BroadcastTaskNotFound { id: String },

    #[error("Proxy not found: {id}")]
    ProxyNotFound { id: String },

    // ================================
    // 业务逻辑错误
    // ================================
    #[error("Port quota exceeded for tenant {tenant_id}")]
    PortQuotaExceeded { tenant_id: String },

    #[error("No available ports in range")]
    NoAvailablePorts,

    #[error("No available proxies")]
    NoAvailableProxies,

    #[error("WhatsApp account already exists for phone number: {phone}")]
    WhatsAppAccountAlreadyExists { phone: String },

    #[error("Chat session already assigned to agent: {agent_id}")]
    ChatSessionAlreadyAssigned { agent_id: String },

    #[error("Broadcast task already scheduled")]
    BroadcastTaskAlreadyScheduled,

    #[error("Cannot delete tenant with active WhatsApp accounts")]
    TenantHasActiveAccounts,

    // ================================
    // 外部服务错误
    // ================================
    #[error("Container operation failed: {message}")]
    ContainerError { message: String },

    #[error("Container not found: {container_id}")]
    ContainerNotFound { container_id: String },

    #[error("Proxy connection failed: {proxy_id}")]
    ProxyConnectionFailed { proxy_id: String },

    #[error("WhatsApp API error: {message}")]
    WhatsAppApiError { message: String },

    // ================================
    // 网络和通信错误
    // ================================
    #[error("HTTP request failed: {message}")]
    HttpRequestFailed { message: String },

    #[error("WebSocket connection failed: {message}")]
    WebSocketConnectionFailed { message: String },

    #[error("Serialization error: {message}")]
    SerializationError { message: String },

    // ================================
    // 配置和环境错误
    // ================================
    #[error("Configuration error: {message}")]
    Configuration { message: String },

    #[error("Environment variable not found: {var_name}")]
    EnvironmentVariableNotFound { var_name: String },

    #[error("Invalid configuration value: {key} = {value}")]
    InvalidConfigurationValue { key: String, value: String },

    // ================================
    // 内部错误
    // ================================
    #[error("Internal server error: {message}")]
    Internal { message: String },

    #[error("Service unavailable: {service}")]
    ServiceUnavailable { service: String },

    #[error("Operation timeout")]
    Timeout,

    #[error("Concurrent modification detected")]
    ConcurrentModification,
}

impl AppError {
    /// 创建数据库错误
    pub fn database<T: std::fmt::Display>(message: T) -> Self {
        Self::Database {
            message: message.to_string(),
        }
    }

    /// 创建验证错误
    pub fn validation<T: std::fmt::Display>(message: T) -> Self {
        Self::Validation {
            message: message.to_string(),
        }
    }

    /// 创建无效输入错误
    pub fn invalid_input<F: std::fmt::Display, M: std::fmt::Display>(field: F, message: M) -> Self {
        Self::InvalidInput {
            field: field.to_string(),
            message: message.to_string(),
        }
    }

    /// 创建内部错误
    pub fn internal<T: std::fmt::Display>(message: T) -> Self {
        Self::Internal {
            message: message.to_string(),
        }
    }

    /// 检查是否是客户端错误（4xx）
    pub fn is_client_error(&self) -> bool {
        matches!(
            self,
            AppError::Validation { .. }
                | AppError::InvalidInput { .. }
                | AppError::RequiredFieldMissing { .. }
                | AppError::AuthenticationFailed
                | AppError::InvalidCredentials
                | AppError::TokenExpired
                | AppError::TokenInvalid
                | AppError::TokenRevoked
                | AppError::PermissionDenied
                | AppError::InsufficientPrivileges
                | AppError::TenantNotFound { .. }
                | AppError::UserNotFound { .. }
                | AppError::WhatsAppAccountNotFound { .. }
                | AppError::ChatSessionNotFound { .. }
                | AppError::MessageNotFound { .. }
                | AppError::BroadcastTaskNotFound { .. }
                | AppError::ProxyNotFound { .. }
                | AppError::PortQuotaExceeded { .. }
                | AppError::WhatsAppAccountAlreadyExists { .. }
                | AppError::ChatSessionAlreadyAssigned { .. }
                | AppError::BroadcastTaskAlreadyScheduled
                | AppError::TenantHasActiveAccounts
        )
    }

    /// 检查是否是服务器错误（5xx）
    pub fn is_server_error(&self) -> bool {
        !self.is_client_error()
    }

    /// 获取 HTTP 状态码
    pub fn status_code(&self) -> u16 {
        match self {
            // 400 Bad Request
            AppError::Validation { .. }
            | AppError::InvalidInput { .. }
            | AppError::RequiredFieldMissing { .. } => 400,

            // 401 Unauthorized
            AppError::AuthenticationFailed
            | AppError::InvalidCredentials
            | AppError::TokenExpired
            | AppError::TokenInvalid
            | AppError::TokenRevoked => 401,

            // 403 Forbidden
            AppError::PermissionDenied | AppError::InsufficientPrivileges => 403,

            // 404 Not Found
            AppError::TenantNotFound { .. }
            | AppError::UserNotFound { .. }
            | AppError::WhatsAppAccountNotFound { .. }
            | AppError::ChatSessionNotFound { .. }
            | AppError::MessageNotFound { .. }
            | AppError::BroadcastTaskNotFound { .. }
            | AppError::ProxyNotFound { .. }
            | AppError::ContainerNotFound { .. } => 404,

            // 409 Conflict
            AppError::WhatsAppAccountAlreadyExists { .. }
            | AppError::ChatSessionAlreadyAssigned { .. }
            | AppError::BroadcastTaskAlreadyScheduled
            | AppError::ConcurrentModification => 409,

            // 422 Unprocessable Entity
            AppError::PortQuotaExceeded { .. }
            | AppError::NoAvailablePorts
            | AppError::NoAvailableProxies
            | AppError::TenantHasActiveAccounts => 422,

            // 503 Service Unavailable
            AppError::ServiceUnavailable { .. } => 503,

            // 504 Gateway Timeout
            AppError::Timeout => 504,

            // 500 Internal Server Error (默认)
            _ => 500,
        }
    }
}

/// 应用程序的 Result 类型别名
pub type Result<T> = std::result::Result<T, AppError>;

// ================================
// 外部错误类型的转换
// ================================

impl From<sqlx::Error> for AppError {
    fn from(err: sqlx::Error) -> Self {
        AppError::Database {
            message: err.to_string(),
        }
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::SerializationError {
            message: err.to_string(),
        }
    }
}

impl From<uuid::Error> for AppError {
    fn from(err: uuid::Error) -> Self {
        AppError::InvalidInput {
            field: "id".to_string(),
            message: err.to_string(),
        }
    }
}

// ================================
// 测试模块
// ================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let err = AppError::validation("Invalid email format");
        assert!(matches!(err, AppError::Validation { .. }));
        assert!(err.is_client_error());
        assert_eq!(err.status_code(), 400);
    }

    #[test]
    fn test_error_status_codes() {
        assert_eq!(AppError::AuthenticationFailed.status_code(), 401);
        assert_eq!(AppError::PermissionDenied.status_code(), 403);
        assert_eq!(
            AppError::TenantNotFound {
                id: "test".to_string()
            }
            .status_code(),
            404
        );
        assert_eq!(
            AppError::Internal {
                message: "test".to_string()
            }
            .status_code(),
            500
        );
    }

    #[test]
    fn test_client_vs_server_errors() {
        assert!(AppError::Validation {
            message: "test".to_string()
        }
        .is_client_error());
        assert!(!AppError::Validation {
            message: "test".to_string()
        }
        .is_server_error());

        assert!(AppError::Internal {
            message: "test".to_string()
        }
        .is_server_error());
        assert!(!AppError::Internal {
            message: "test".to_string()
        }
        .is_client_error());
    }

    #[test]
    fn test_error_serialization() {
        let err = AppError::TenantNotFound {
            id: "123".to_string(),
        };
        let json = serde_json::to_string(&err).unwrap();
        let deserialized: AppError = serde_json::from_str(&json).unwrap();

        match deserialized {
            AppError::TenantNotFound { id } => assert_eq!(id, "123"),
            _ => panic!("Unexpected error type"),
        }
    }
}
